package io.hydrax.pricestreaming.events;

import com.google.protobuf.TextFormat;
import io.hydrax.aeron.client.ClientManager;
import io.hydrax.pricestreaming.common.Constant;
import io.hydrax.pricestreaming.domain.Order;
import io.hydrax.pricestreaming.service.OrderService;
import io.quarkus.vertx.ConsumeEvent;
import io.smallrye.common.annotation.RunOnVirtualThread;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ApplicationScoped
public class OrderEvent {

  final OrderService orderService;
  final ClientManager clientManager;

  @ConsumeEvent(Constant.ORDER)
  @RunOnVirtualThread
  @SuppressWarnings("unused")
  public void onOrder(Order order) {
    // TODO: need to refactor
    if (log.isTraceEnabled()) {
      log.trace("OrderEvent: {}", TextFormat.shortDebugString(order.getPsOrder()));
    }
    switch (order.getPsOrder().getRequestType()) {
      case REQUEST_TYPE_NEW_ORDER:
        log.trace("New order");
        orderService.placeOrder(order);
        break;
      case REQUEST_TYPE_EDIT_ORDER:
        log.trace("Amend order");
        break;
      case REQUEST_TYPE_CANCEL_ORDER:
        log.trace("Cancel order");
        orderService.cancelOrder(order.getPsOrder());
        break;
      default:
        if (log.isDebugEnabled()) {
          log.debug("Unknown order type: {}", TextFormat.shortDebugString(order.getPsOrder()));
        }
    }
  }
}
